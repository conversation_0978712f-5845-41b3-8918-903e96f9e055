# Service Key Configuration Guide

Panduan konfigurasi service key untuk komunikasi internal antar microservices.

## 🔑 Internal Service Key

Semua microservices menggunakan **satu key yang sama** untuk autentikasi komunikasi internal:

```
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
```

## 📍 Lokasi Konfigurasi

### 1. Shared Configuration (`.env.shared`)

Key utama didefinisikan di `.env.shared` baris 32:

```env
# Internal Service Communication Key
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
```

### 2. Service-Specific Configurations

Setiap service menggunakan key yang sama dengan nama variable yang berbeda:

#### Analysis Worker (`.env`)
```env
# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3002
# Use the same key as INTERNAL_SERVICE_KEY from .env.shared
ARCHIVE_SERVICE_KEY=internal_service_secret_key_change_in_production

# Notification Service Configuration (Optional)
NOTIFICATION_SERVICE_URL=http://localhost:3005
# Use the same key as INTERNAL_SERVICE_KEY from .env.shared
NOTIFICATION_SERVICE_KEY=internal_service_secret_key_change_in_production
```

#### Archive Service (`.env`)
```env
# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
```

#### Assessment Service (`.env`)
```env
# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
```

## 🔄 Service Communication Flow

### 1. Analysis Worker → Archive Service

**Headers yang dikirim:**
```
X-Internal-Service: true
X-Service-Key: internal_service_secret_key_change_in_production
Content-Type: application/json
```

**Verifikasi di Archive Service:**
```javascript
// archive-service/src/middleware/auth.js
const serviceAuth = (req, res, next) => {
  const serviceKey = req.headers['x-service-key'];
  const isInternalService = req.headers['x-internal-service'] === 'true';
  
  if (isInternalService && serviceKey === process.env.INTERNAL_SERVICE_KEY) {
    req.isInternalService = true;
    return next();
  }
  
  // Reject unauthorized requests
  return res.status(401).json({ error: 'Unauthorized' });
};
```

### 2. Analysis Worker → Notification Service (Optional)

**Headers yang dikirim:**
```
X-Internal-Service: true
X-Service-Key: internal_service_secret_key_change_in_production
Content-Type: application/json
```

## 🛠️ Setup Instructions

### 1. Copy Shared Configuration

Pastikan semua services memuat `.env.shared`:

```bash
# Setiap service folder harus memiliki .env yang load .env.shared
# Atau gunakan dotenv dengan path ke .env.shared
```

### 2. Verify Service Keys

Pastikan semua service menggunakan key yang sama:

```bash
# Check Analysis Worker
grep "ARCHIVE_SERVICE_KEY" analysis-worker/.env

# Check Archive Service  
grep "INTERNAL_SERVICE_KEY" archive-service/.env

# Check Assessment Service
grep "INTERNAL_SERVICE_KEY" assessment-service/.env
```

### 3. Test Service Communication

Gunakan script testing untuk memverifikasi komunikasi:

```bash
# Run E2E test
npm run test:e2e

# Or manual test
node scripts/test-end-to-end.js
```

## 🔒 Security Best Practices

### 1. Production Key

**PENTING**: Ganti key default di production:

```env
# Production .env.shared
INTERNAL_SERVICE_KEY=your_super_secure_production_key_here_min_32_chars
```

### 2. Key Requirements

- **Minimum 32 karakter**
- **Kombinasi huruf, angka, dan simbol**
- **Unik untuk setiap environment**
- **Tidak boleh di-commit ke version control**

### 3. Environment-Specific Keys

```env
# Development
INTERNAL_SERVICE_KEY=dev_internal_service_key_2024_atma_backend

# Staging  
INTERNAL_SERVICE_KEY=staging_internal_service_key_2024_atma_backend

# Production
INTERNAL_SERVICE_KEY=prod_internal_service_key_2024_atma_backend_secure
```

## 🐛 Troubleshooting

### 1. Authentication Failed

**Error:**
```
❌ Failed to save analysis result: Request failed with status code 401
```

**Solution:**
- Pastikan `ARCHIVE_SERVICE_KEY` di analysis-worker sama dengan `INTERNAL_SERVICE_KEY` di archive-service
- Check headers yang dikirim: `X-Internal-Service: true` dan `X-Service-Key`

### 2. Service Key Mismatch

**Error:**
```
❌ Unauthorized service request
```

**Solution:**
```bash
# Check key di analysis-worker
echo $ARCHIVE_SERVICE_KEY

# Check key di archive-service  
echo $INTERNAL_SERVICE_KEY

# Pastikan sama
```

### 3. Missing Headers

**Error:**
```
❌ Missing required service headers
```

**Solution:**
Pastikan request include headers:
```javascript
headers: {
  'X-Internal-Service': 'true',
  'X-Service-Key': process.env.ARCHIVE_SERVICE_KEY,
  'Content-Type': 'application/json'
}
```

## 📋 Checklist Setup

- [ ] `.env.shared` memiliki `INTERNAL_SERVICE_KEY`
- [ ] Analysis Worker `.env` memiliki `ARCHIVE_SERVICE_KEY` dengan value yang sama
- [ ] Archive Service `.env` memiliki `INTERNAL_SERVICE_KEY` dengan value yang sama
- [ ] Assessment Service `.env` memiliki `INTERNAL_SERVICE_KEY` dengan value yang sama
- [ ] Semua services dapat berkomunikasi (test dengan E2E)
- [ ] Production key sudah diganti dari default

## 🔗 Related Files

- `.env.shared` - Shared configuration
- `analysis-worker/.env.example` - Analysis Worker configuration template
- `archive-service/.env.example` - Archive Service configuration template
- `scripts/test-end-to-end.js` - E2E testing script
- `analysis-worker/src/services/archiveService.js` - Service communication implementation
- `archive-service/src/middleware/auth.js` - Service authentication middleware
