/**
 * Google Generative AI Service
 */

const { Type } = require('@google/genai');
const ai = require('../config/ai');
const logger = require('../utils/logger');
const { validatePersonaProfile } = require('../utils/validator');

/**
 * Initialize AI service
 */
const initialize = () => {
  try {
    // Initialize Google Generative AI
    ai.initialize();
    
    logger.info('AI service initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize AI service', { error: error.message });
    throw error;
  }
};

/**
 * Generate persona profile from assessment data
 * @param {Object} assessmentData - Assessment data
 * @param {String} jobId - Job ID for logging
 * @returns {Promise<Array>} - Persona profile
 */
const generatePersonaProfile = async (assessmentData, jobId) => {
  try {
    logger.info('Generating persona profile', { jobId });

    // Get AI client
    const client = ai.getClient();

    // Build prompt
    const prompt = buildPrompt(assessmentData);

    // Define response schema for structured output
    const responseSchema = {
      type: Type.ARRAY,
      description: "Array berisi analisis persona profile",
      minItems: 1,
      maxItems: 1,
      items: {
        type: Type.OBJECT,
        description: "Objek persona profile yang komprehensif",
        properties: {
          archetype: {
            type: Type.STRING,
            description: "Nama archetype yang paling sesuai dengan persona"
          },
          shortSummary: {
            type: Type.STRING,
            description: "Ringkasan singkat tentang persona (1 atau 2 paragraf terpisah)"
          },
          strengths: {
            type: Type.ARRAY,
            description: "Daftar kekuatan/strength dari persona",
            minItems: 3,
            maxItems: 5,
            items: {
              type: Type.STRING,
              description: "Kekuatan spesifik dari persona"
            }
          },
          weakness: {
            type: Type.ARRAY,
            description: "Daftar kelemahan/weakness dari persona",
            minItems: 3,
            maxItems: 5,
            items: {
              type: Type.STRING,
              description: "Kelemahan atau area pengembangan dari persona"
            }
          },
          careerRecommendation: {
            type: Type.ARRAY,
            description: "Daftar rekomendasi karir yang sesuai dengan persona",
            minItems: 3,
            maxItems: 5,
            items: {
              type: Type.STRING,
              description: "Rekomendasi karir spesifik"
            }
          },
          insights: {
            type: Type.ARRAY,
            description: "Daftar insight atau saran pengembangan diri",
            minItems: 3,
            maxItems: 5,
            items: {
              type: Type.STRING,
              description: "Insight atau saran actionable untuk pengembangan"
            }
          },
          workEnvironment: {
            type: Type.STRING,
            description: "Deskripsi lingkungan kerja yang ideal untuk persona secara singkat da"
          },
          roleModel: {
            type: Type.ARRAY,
            description: "Daftar role model yang relevan dan inspiratif",
            minItems: 4,
            maxItems: 5,
            items: {
              type: Type.STRING,
              description: "Nama role model yang inspiratif dan relevan"
            }
          }
        },
        required: [
          "archetype", "shortSummary", "strengths", "weakness",
          "careerRecommendation", "insights", "workEnvironment", "roleModel"
        ],
        propertyOrdering: [
          "archetype", "shortSummary", "strengths", "weakness",
          "careerRecommendation", "insights", "workEnvironment", "roleModel"
        ]
      }
    };

    // Generate content with structured output
    const response = await client.models.generateContent({
      model: ai.config.model,
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        responseSchema: responseSchema,
        temperature: ai.config.temperature
        // Tidak menggunakan maxOutputTokens agar output tidak terpotong
      }
    });

    logger.info('AI response received', {
      jobId,
      responseLength: response.text.length,
      thoughtsTokenCount: response.usageMetadata?.thoughtsTokenCount || 0,
      candidatesTokenCount: response.usageMetadata?.candidatesTokenCount || 0
    });

    // Parse JSON response directly (no need for manual parsing)
    const personaProfile = JSON.parse(response.text);

    // Validate persona profile
    const validationResult = validatePersonaProfile(personaProfile);
    if (!validationResult.isValid) {
      throw new Error(`Invalid persona profile: ${validationResult.error}`);
    }

    return personaProfile;
  } catch (error) {
    logger.error('Failed to generate persona profile', {
      jobId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Build prompt for AI
 * @param {Object} assessmentData - Assessment data
 * @returns {String} - Prompt for AI
 */
const buildPrompt = (assessmentData) => {
  // Extract assessment data
  const { riasec, ocean, viaIs, multipleIntelligences, cognitiveStyleIndex } = assessmentData;

  // Build VIA-IS section
  const viaIsSection = Object.entries(viaIs)
    .map(([key, value]) => `- ${formatCamelCase(key)}: ${value}/100`)
    .join('\n');

  // Build Multiple Intelligences section
  const miSection = Object.entries(multipleIntelligences)
    .map(([key, value]) => `- ${formatCamelCase(key)}: ${value}/100`)
    .join('\n');

  // Build prompt (simplified since we're using structured output)
  return `
Anda adalah seorang psikolog profesional yang ahli dalam analisis kepribadian dan pemetaan bakat.
Berdasarkan hasil assessment berikut, buatlah analisis persona yang komprehensif:

RIASEC Assessment:
- Realistic: ${riasec.realistic}/100
- Investigative: ${riasec.investigative}/100
- Artistic: ${riasec.artistic}/100
- Social: ${riasec.social}/100
- Enterprising: ${riasec.enterprising}/100
- Conventional: ${riasec.conventional}/100

OCEAN Personality Assessment:
- Openness: ${ocean.openness}/100
- Conscientiousness: ${ocean.conscientiousness}/100
- Extraversion: ${ocean.extraversion}/100
- Agreeableness: ${ocean.agreeableness}/100
- Neuroticism: ${ocean.neuroticism}/100

VIA Character Strengths:
${viaIsSection}

Multiple Intelligences:
${miSection}

Cognitive Style Index:
- Analytic: ${cognitiveStyleIndex.analytic}/100
- Intuitive: ${cognitiveStyleIndex.intuitive}/100

Buatlah analisis persona yang:
1. Akurat berdasarkan data assessment
2. Memberikan insight yang mendalam dan actionable
3. Rekomendasi karir yang realistis dan sesuai
4. Menggunakan bahasa Indonesia yang profesional
5. Memberikan role model yang relevan dan inspiratif

Berikan analisis dalam format yang telah ditentukan.
`;
};



/**
 * Format camelCase to Title Case
 * @param {String} text - camelCase text
 * @returns {String} - Title Case text
 */
const formatCamelCase = (text) => {
  // Convert camelCase to space-separated
  const spaced = text.replace(/([A-Z])/g, ' $1');
  
  // Convert to Title Case
  return spaced.charAt(0).toUpperCase() + spaced.slice(1);
};

module.exports = {
  initialize,
  generatePersonaProfile
};
