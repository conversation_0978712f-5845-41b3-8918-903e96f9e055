/**
 * Google Generative AI Service
 */

const { Type } = require('@google/genai');
const ai = require('../config/ai');
const logger = require('../utils/logger');
const { validatePersonaProfile } = require('../utils/validator');

/**
 * Initialize AI service
 */
const initialize = () => {
  try {
    // Initialize Google Generative AI
    ai.initialize();
    
    logger.info('AI service initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize AI service', { error: error.message });
    throw error;
  }
};

/**
 * Generate persona profile from assessment data
 * @param {Object} assessmentData - Assessment data
 * @param {String} jobId - Job ID for logging
 * @returns {Promise<Array>} - Persona profile
 */
const generatePersonaProfile = async (assessmentData, jobId) => {
  try {
    logger.info('Generating persona profile', { jobId });

    // Get AI client
    const client = ai.getClient();

    // Build prompt
    const prompt = buildPrompt(assessmentData);

    // Define response schema for structured output
    const responseSchema = {
      type: Type.ARRAY,
      items: {
        type: Type.OBJECT,
        properties: {
          archetype: {
            type: Type.STRING,
            description: "Nama archetype yang paling sesuai dengan persona"
          },
          shortSummary: {
            type: Type.STRING,
            description: "Ringkasan singkat tentang persona (1-2 paragraf)"
          },
          strengths: {
            type: Type.ARRAY,
            items: { type: Type.STRING },
            description: "Daftar kekuatan/strength dari persona"
          },
          weakness: {
            type: Type.ARRAY,
            items: { type: Type.STRING },
            description: "Daftar kelemahan/weakness dari persona"
          },
          careerRecommendation: {
            type: Type.ARRAY,
            items: { type: Type.STRING },
            description: "Daftar rekomendasi karir yang sesuai dengan persona"
          },
          insights: {
            type: Type.ARRAY,
            items: { type: Type.STRING },
            description: "Daftar insight atau saran pengembangan diri"
          },
          workEnvironment: {
            type: Type.STRING,
            description: "Deskripsi lingkungan kerja yang ideal untuk persona"
          },
          roleModel: {
            type: Type.ARRAY,
            items: { type: Type.STRING },
            description: "Daftar role model yang relevan dan inspiratif"
          }
        },
        propertyOrdering: [
          "archetype", "shortSummary", "strengths", "weakness",
          "careerRecommendation", "insights", "workEnvironment", "roleModel"
        ]
      }
    };

    // Generate content with structured output
    const response = await client.models.generateContent({
      model: ai.config.model,
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        responseSchema: responseSchema,
        temperature: ai.config.temperature,
        maxOutputTokens: ai.config.maxTokens
      }
    });

    logger.info('AI response received', {
      jobId,
      responseLength: response.text.length,
      thoughtsTokenCount: response.usageMetadata?.thoughtsTokenCount || 0,
      candidatesTokenCount: response.usageMetadata?.candidatesTokenCount || 0
    });

    // Parse JSON response directly (no need for manual parsing)
    const personaProfile = JSON.parse(response.text);

    // Validate persona profile
    const validationResult = validatePersonaProfile(personaProfile);
    if (!validationResult.isValid) {
      throw new Error(`Invalid persona profile: ${validationResult.error}`);
    }

    return personaProfile;
  } catch (error) {
    logger.error('Failed to generate persona profile', {
      jobId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Build prompt for AI
 * @param {Object} assessmentData - Assessment data
 * @returns {String} - Prompt for AI
 */
const buildPrompt = (assessmentData) => {
  // Extract assessment data
  const { riasec, ocean, viaIs, multipleIntelligences, cognitiveStyleIndex } = assessmentData;

  // Build VIA-IS section
  const viaIsSection = Object.entries(viaIs)
    .map(([key, value]) => `- ${formatCamelCase(key)}: ${value}/100`)
    .join('\n');

  // Build Multiple Intelligences section
  const miSection = Object.entries(multipleIntelligences)
    .map(([key, value]) => `- ${formatCamelCase(key)}: ${value}/100`)
    .join('\n');

  // Build prompt (simplified since we're using structured output)
  return `
Anda adalah seorang psikolog profesional yang ahli dalam analisis kepribadian dan pemetaan bakat.
Berdasarkan hasil assessment berikut, buatlah analisis persona yang komprehensif:

RIASEC Assessment:
- Realistic: ${riasec.realistic}/100
- Investigative: ${riasec.investigative}/100
- Artistic: ${riasec.artistic}/100
- Social: ${riasec.social}/100
- Enterprising: ${riasec.enterprising}/100
- Conventional: ${riasec.conventional}/100

OCEAN Personality Assessment:
- Openness: ${ocean.openness}/100
- Conscientiousness: ${ocean.conscientiousness}/100
- Extraversion: ${ocean.extraversion}/100
- Agreeableness: ${ocean.agreeableness}/100
- Neuroticism: ${ocean.neuroticism}/100

VIA Character Strengths:
${viaIsSection}

Multiple Intelligences:
${miSection}

Cognitive Style Index:
- Analytic: ${cognitiveStyleIndex.analytic}/100
- Intuitive: ${cognitiveStyleIndex.intuitive}/100

Buatlah analisis persona yang:
1. Akurat berdasarkan data assessment
2. Memberikan insight yang mendalam dan actionable
3. Rekomendasi karir yang realistis dan sesuai
4. Menggunakan bahasa Indonesia yang profesional
5. Memberikan role model yang relevan dan inspiratif

Berikan analisis dalam format yang telah ditentukan.
`;
};



/**
 * Format camelCase to Title Case
 * @param {String} text - camelCase text
 * @returns {String} - Title Case text
 */
const formatCamelCase = (text) => {
  // Convert camelCase to space-separated
  const spaced = text.replace(/([A-Z])/g, ' $1');
  
  // Convert to Title Case
  return spaced.charAt(0).toUpperCase() + spaced.slice(1);
};

module.exports = {
  initialize,
  generatePersonaProfile
};
