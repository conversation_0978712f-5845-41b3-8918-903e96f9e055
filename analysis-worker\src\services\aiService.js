/**
 * Google Generative AI Service
 */

const ai = require('../config/ai');
const logger = require('../utils/logger');
const { validatePersonaProfile } = require('../utils/validator');

/**
 * Initialize AI service
 */
const initialize = () => {
  try {
    // Initialize Google Generative AI
    ai.initialize();
    
    logger.info('AI service initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize AI service', { error: error.message });
    throw error;
  }
};

/**
 * Generate persona profile from assessment data
 * @param {Object} assessmentData - Assessment data
 * @param {String} jobId - Job ID for logging
 * @returns {Promise<Array>} - Persona profile
 */
const generatePersonaProfile = async (assessmentData, jobId) => {
  try {
    logger.info('Generating persona profile', { jobId });
    
    // Get AI model
    const model = ai.getModel();
    
    // Build prompt
    const prompt = buildPrompt(assessmentData);
    
    // Generate content
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    logger.info('AI response received', { 
      jobId,
      responseLength: text.length,
      promptTokens: result.response.promptFeedback?.tokenCount || 0
    });
    
    // Parse and validate response
    const personaProfile = parseAIResponse(text);
    
    // Validate persona profile
    const validationResult = validatePersonaProfile(personaProfile);
    if (!validationResult.isValid) {
      throw new Error(`Invalid persona profile: ${validationResult.error}`);
    }
    
    return personaProfile;
  } catch (error) {
    logger.error('Failed to generate persona profile', { 
      jobId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Build prompt for AI
 * @param {Object} assessmentData - Assessment data
 * @returns {String} - Prompt for AI
 */
const buildPrompt = (assessmentData) => {
  // Extract assessment data
  const { riasec, ocean, viaIs, multipleIntelligences, cognitiveStyleIndex } = assessmentData;
  
  // Build VIA-IS section
  const viaIsSection = Object.entries(viaIs)
    .map(([key, value]) => `- ${formatCamelCase(key)}: ${value}/100`)
    .join('\n');
  
  // Build Multiple Intelligences section
  const miSection = Object.entries(multipleIntelligences)
    .map(([key, value]) => `- ${formatCamelCase(key)}: ${value}/100`)
    .join('\n');
  
  // Build prompt
  return `
Anda adalah seorang psikolog profesional yang ahli dalam analisis kepribadian dan pemetaan bakat. 
Berdasarkan hasil assessment berikut, buatlah analisis persona yang komprehensif:

RIASEC Assessment:
- Realistic: ${riasec.realistic}/100
- Investigative: ${riasec.investigative}/100
- Artistic: ${riasec.artistic}/100
- Social: ${riasec.social}/100
- Enterprising: ${riasec.enterprising}/100
- Conventional: ${riasec.conventional}/100

OCEAN Personality Assessment:
- Openness: ${ocean.openness}/100
- Conscientiousness: ${ocean.conscientiousness}/100
- Extraversion: ${ocean.extraversion}/100
- Agreeableness: ${ocean.agreeableness}/100
- Neuroticism: ${ocean.neuroticism}/100

VIA Character Strengths:
${viaIsSection}

Multiple Intelligences:
${miSection}

Cognitive Style Index:
- Analytic: ${cognitiveStyleIndex.analytic}/100
- Intuitive: ${cognitiveStyleIndex.intuitive}/100

Berdasarkan data di atas, buatlah analisis dalam format JSON yang sesuai dengan schema berikut:
[
  {
    "archetype": "Nama archetype yang paling sesuai",
    "shortSummary": "Ringkasan singkat tentang persona (1-2 paragraf)",
    "strengths": ["Kekuatan 1", "Kekuatan 2", "Kekuatan 3", ...],
    "weakness": ["Kelemahan 1", "Kelemahan 2", "Kelemahan 3", ...],
    "careerRecommendation": ["Karir 1", "Karir 2", "Karir 3", ...],
    "insights": ["Insight 1", "Insight 2", "Insight 3", ...],
    "workEnvironment": "Deskripsi lingkungan kerja yang ideal",
    "roleModel": ["Role Model 1", "Role Model 2", "Role Model 3", ...]
  }
]

Pastikan analisis Anda:
1. Akurat berdasarkan data assessment
2. Memberikan insight yang mendalam dan actionable
3. Rekomendasi karir yang realistis dan sesuai
4. Menggunakan bahasa Indonesia yang profesional
5. Memberikan role model yang relevan dan inspiratif

Berikan respons HANYA dalam format JSON yang valid, tanpa penjelasan tambahan.
`;
};

/**
 * Parse AI response to extract persona profile
 * @param {String} response - AI response text
 * @returns {Array} - Parsed persona profile
 */
const parseAIResponse = (response) => {
  try {
    // Extract JSON from response
    const jsonMatch = response.match(/\[[\s\S]*\]/);
    
    if (!jsonMatch) {
      throw new Error('No JSON found in AI response');
    }
    
    // Parse JSON
    const personaProfile = JSON.parse(jsonMatch[0]);
    
    return personaProfile;
  } catch (error) {
    logger.error('Failed to parse AI response', { error: error.message });
    throw new Error(`Failed to parse AI response: ${error.message}`);
  }
};

/**
 * Format camelCase to Title Case
 * @param {String} text - camelCase text
 * @returns {String} - Title Case text
 */
const formatCamelCase = (text) => {
  // Convert camelCase to space-separated
  const spaced = text.replace(/([A-Z])/g, ' $1');
  
  // Convert to Title Case
  return spaced.charAt(0).toUpperCase() + spaced.slice(1);
};

module.exports = {
  initialize,
  generatePersonaProfile
};
