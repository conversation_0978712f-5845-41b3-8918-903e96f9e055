{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-16 03:59:10","version":"1.0.0"}
{"level":"info","message":"AI service initialized successfully","service":"analysis-worker","timestamp":"2025-07-16 03:59:10","version":"1.0.0"}
{"jobId":"test-direct-123","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-16 03:59:10","version":"1.0.0"}
{"candidatesTokenCount":797,"jobId":"test-direct-123","level":"info","message":"AI response received","responseLength":3693,"service":"analysis-worker","thoughtsTokenCount":1160,"timestamp":"2025-07-16 03:59:21","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-16 04:00:10","version":"1.0.0"}
{"level":"info","message":"AI service initialized successfully","service":"analysis-worker","timestamp":"2025-07-16 04:00:10","version":"1.0.0"}
{"jobId":"test-db-flow-123","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-16 04:00:10","version":"1.0.0"}
{"candidatesTokenCount":942,"jobId":"test-db-flow-123","level":"info","message":"AI response received","responseLength":4369,"service":"analysis-worker","thoughtsTokenCount":1710,"timestamp":"2025-07-16 04:00:25","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-16 04:00:48","version":"1.0.0"}
{"level":"info","message":"AI service initialized successfully","service":"analysis-worker","timestamp":"2025-07-16 04:00:48","version":"1.0.0"}
{"jobId":"test-db-flow-123","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-16 04:00:48","version":"1.0.0"}
{"candidatesTokenCount":722,"jobId":"test-db-flow-123","level":"info","message":"AI response received","responseLength":3157,"service":"analysis-worker","thoughtsTokenCount":1782,"timestamp":"2025-07-16 04:01:03","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-16 04:01:31","version":"1.0.0"}
{"level":"info","message":"AI service initialized successfully","service":"analysis-worker","timestamp":"2025-07-16 04:01:31","version":"1.0.0"}
{"jobId":"test-db-flow-123","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-16 04:01:31","version":"1.0.0"}
{"candidatesTokenCount":680,"jobId":"test-db-flow-123","level":"info","message":"AI response received","responseLength":3015,"service":"analysis-worker","thoughtsTokenCount":1038,"timestamp":"2025-07-16 04:01:42","version":"1.0.0"}
