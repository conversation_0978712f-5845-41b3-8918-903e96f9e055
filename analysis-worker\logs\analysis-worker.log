{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-15 18:21:45","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-15 18:21:45","url":"amqp://localhost:5672","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-15 18:48:46","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-15 18:48:46","url":"amqp://localhost:5672","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-15 19:02:14","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-15 19:02:14","url":"amqp://localhost:5672","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-15 19:03:57","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-15 19:03:57","url":"amqp://localhost:5672","version":"1.0.0"}
{"level":"info","maxTokens":4096,"message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-16 03:45:26","version":"1.0.0"}
{"level":"info","message":"AI service initialized successfully","service":"analysis-worker","timestamp":"2025-07-16 03:45:26","version":"1.0.0"}
{"jobId":"test-job-123","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-16 03:45:26","version":"1.0.0"}
{"candidatesTokenCount":921,"jobId":"test-job-123","level":"info","message":"AI response received","responseLength":4033,"service":"analysis-worker","thoughtsTokenCount":2008,"timestamp":"2025-07-16 03:45:42","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-16 03:50:54","version":"1.0.0"}
{"level":"info","message":"AI service initialized successfully","service":"analysis-worker","timestamp":"2025-07-16 03:50:54","version":"1.0.0"}
{"jobId":"test-schema-123","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-16 03:50:54","version":"1.0.0"}
{"candidatesTokenCount":698,"jobId":"test-schema-123","level":"info","message":"AI response received","responseLength":2937,"service":"analysis-worker","thoughtsTokenCount":1114,"timestamp":"2025-07-16 03:51:06","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-16 03:56:17","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-16 03:56:17","url":"amqp://localhost:5672","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-16 03:58:15","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-16 03:58:15","url":"amqp://localhost:5672","version":"1.0.0"}
