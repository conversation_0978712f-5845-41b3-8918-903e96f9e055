/**
 * Database Flow Test
 * Tests the complete flow: Assessment Data → AI Processing → Database Storage → Retrieval
 * This simulates the full end-to-end flow without RabbitMQ
 */

require('dotenv').config({ path: '.env.shared' });
require('dotenv').config({ path: 'analysis-worker/.env' });

const { Client } = require('pg');
const path = require('path');

// Add analysis-worker to module path
process.env.NODE_PATH = path.join(__dirname, '../analysis-worker/src');
require('module').Module._initPaths();

// Import AI service
const aiService = require('../analysis-worker/src/services/aiService');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'atma_db',
  user: process.env.DB_USER || 'atma_user',
  password: process.env.DB_PASSWORD || 'secret-passworrd'
};

// Test data
const assessmentData = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    openness: 80,
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30
  },
  viaIs: {
    creativity: 85,
    curiosity: 78,
    judgment: 70,
    loveOfLearning: 82,
    perspective: 75,
    bravery: 68,
    perseverance: 80,
    honesty: 85,
    zest: 70,
    love: 65,
    kindness: 78,
    socialIntelligence: 72,
    teamwork: 75,
    fairness: 80,
    leadership: 68,
    forgiveness: 70,
    humility: 65,
    prudence: 75,
    selfRegulation: 78,
    appreciationOfBeauty: 82,
    gratitude: 85,
    hope: 80,
    humor: 70,
    spirituality: 60
  },
  multipleIntelligences: {
    linguisticVerbal: 85,
    logicalMathematical: 90,
    spatialVisual: 75,
    bodilyKinesthetic: 60,
    musicalRhythmic: 70,
    interpersonal: 65,
    intrapersonal: 80,
    naturalistic: 55,
    existential: 70
  },
  cognitiveStyleIndex: {
    analytic: 80,
    intuitive: 60
  }
};

let dbClient;
let testUserId = '12345678-1234-1234-1234-123456789012'; // Valid UUID format
let resultId;

/**
 * Initialize database connection
 */
async function initDatabase() {
  console.log('🔌 Connecting to database...');

  try {
    dbClient = new Client(dbConfig);
    await dbClient.connect();
    console.log('✅ Database connected successfully');

    // Create or get test user
    await createTestUser();

    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

/**
 * Create test user if not exists
 */
async function createTestUser() {
  console.log('👤 Setting up test user...');

  try {
    // Check if user exists
    const checkQuery = 'SELECT id FROM auth.users WHERE id = $1';
    const checkResult = await dbClient.query(checkQuery, [testUserId]);

    if (checkResult.rows.length === 0) {
      // Create test user
      const insertQuery = `
        INSERT INTO auth.users (id, email, password_hash, created_at, updated_at)
        VALUES ($1, $2, $3, NOW(), NOW())
      `;

      await dbClient.query(insertQuery, [
        testUserId,
        '<EMAIL>',
        '$2b$12$dummy.hash.for.testing.purposes.only'
      ]);

      console.log('✅ Test user created');
    } else {
      console.log('✅ Test user already exists');
    }
  } catch (error) {
    console.error('⚠️  Test user setup failed:', error.message);
    // Continue anyway - might be using existing user
  }
}

/**
 * Test AI processing
 */
async function testAIProcessing() {
  console.log('\n🤖 Testing AI processing...');
  
  try {
    // Initialize AI service
    aiService.initialize();
    console.log('✅ AI service initialized');
    
    // Generate persona profile
    console.log('⏳ Generating persona profile...');
    const personaProfile = await aiService.generatePersonaProfile(
      assessmentData, 
      'test-db-flow-123'
    );
    
    console.log('✅ Persona profile generated successfully');
    console.log(`   Archetype: ${personaProfile[0].archetype}`);
    
    return personaProfile;
  } catch (error) {
    console.error('❌ AI processing failed:', error.message);
    return null;
  }
}

/**
 * Test database storage
 */
async function testDatabaseStorage(personaProfile) {
  console.log('\n💾 Testing database storage...');
  
  try {
    // Insert analysis result
    const insertQuery = `
      INSERT INTO archive.analysis_results (
        id, user_id, status, assessment_data, persona_profile, 
        created_at, updated_at
      ) VALUES (
        gen_random_uuid(), $1, $2, $3, $4, NOW(), NOW()
      ) RETURNING id, created_at
    `;
    
    const result = await dbClient.query(insertQuery, [
      testUserId,
      'completed',
      JSON.stringify(assessmentData),
      JSON.stringify(personaProfile)
    ]);
    
    resultId = result.rows[0].id;
    const createdAt = result.rows[0].created_at;
    
    console.log('✅ Data stored successfully in database');
    console.log(`   Result ID: ${resultId}`);
    console.log(`   User ID: ${testUserId}`);
    console.log(`   Created at: ${createdAt}`);
    
    return true;
  } catch (error) {
    console.error('❌ Database storage failed:', error.message);
    return false;
  }
}

/**
 * Test data retrieval
 */
async function testDataRetrieval() {
  console.log('\n📥 Testing data retrieval...');
  
  try {
    // Retrieve stored data
    const selectQuery = `
      SELECT 
        id, user_id, status, assessment_data, persona_profile,
        created_at, updated_at
      FROM archive.analysis_results 
      WHERE user_id = $1 
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    const result = await dbClient.query(selectQuery, [testUserId]);
    
    if (result.rows.length === 0) {
      throw new Error('No data found in database');
    }
    
    const record = result.rows[0];
    
    console.log('✅ Data retrieved successfully');
    console.log(`   Record ID: ${record.id}`);
    console.log(`   Status: ${record.status}`);
    
    // Validate assessment data
    if (record.assessment_data) {
      const assessmentKeys = Object.keys(record.assessment_data);
      console.log(`   Assessment data keys: ${assessmentKeys.length} types`);
      
      const expectedKeys = ['riasec', 'ocean', 'viaIs', 'multipleIntelligences', 'cognitiveStyleIndex'];
      const hasAllKeys = expectedKeys.every(key => assessmentKeys.includes(key));
      
      if (hasAllKeys) {
        console.log('   ✅ All assessment data types present');
      } else {
        console.log('   ⚠️  Some assessment data missing');
      }
    }
    
    // Validate persona profile
    if (record.persona_profile && Array.isArray(record.persona_profile)) {
      const profile = record.persona_profile[0];
      if (profile && profile.archetype) {
        console.log(`   Persona archetype: ${profile.archetype}`);
        console.log(`   Profile fields: ${Object.keys(profile).length} fields`);
        console.log('   ✅ Persona profile data intact');
      } else {
        console.log('   ⚠️  Persona profile structure invalid');
      }
    } else {
      console.log('   ❌ Persona profile data missing');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Data retrieval failed:', error.message);
    return false;
  }
}

/**
 * Cleanup test data
 */
async function cleanupTestData() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    if (resultId) {
      await dbClient.query(
        'DELETE FROM archive.analysis_results WHERE id = $1',
        [resultId]
      );
      console.log('✅ Test data cleaned up');
    }
  } catch (error) {
    console.error('❌ Cleanup failed:', error.message);
  }
}

/**
 * Close database connection
 */
async function closeDatabase() {
  if (dbClient) {
    await dbClient.end();
    console.log('🔌 Database connection closed');
  }
}

/**
 * Main test function
 */
async function runDatabaseFlowTest() {
  console.log('🚀 Starting Database Flow Test');
  console.log('=' .repeat(60));
  
  let allTestsPassed = true;
  
  try {
    // Initialize database
    const dbOk = await initDatabase();
    if (!dbOk) {
      allTestsPassed = false;
    } else {
      // Test AI processing
      const personaProfile = await testAIProcessing();
      if (!personaProfile) {
        allTestsPassed = false;
      } else {
        // Test database storage
        const storageOk = await testDatabaseStorage(personaProfile);
        if (!storageOk) {
          allTestsPassed = false;
        } else {
          // Test data retrieval
          const retrievalOk = await testDataRetrieval();
          if (!retrievalOk) allTestsPassed = false;
        }
      }
    }
    
  } catch (error) {
    console.error('\n❌ Unexpected error:', error.message);
    allTestsPassed = false;
  } finally {
    // Cleanup
    await cleanupTestData();
    await closeDatabase();
  }
  
  console.log('\n' + '='.repeat(60));
  if (allTestsPassed) {
    console.log('🎉 Database Flow Test PASSED');
    console.log('\nFlow verified:');
    console.log('  ✅ Assessment data input');
    console.log('  ✅ AI processing (Gemini 2.5 Flash)');
    console.log('  ✅ Structured output generation');
    console.log('  ✅ Database storage (PostgreSQL)');
    console.log('  ✅ Data retrieval and validation');
    console.log('  ✅ Complete data integrity');
    console.log('\nThis simulates the core analysis-worker functionality!');
  } else {
    console.log('❌ Database Flow Test FAILED');
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  runDatabaseFlowTest().catch(console.error);
}

module.exports = { runDatabaseFlowTest };
