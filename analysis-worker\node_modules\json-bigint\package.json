{"name": "json-bigint", "version": "1.0.0", "description": "JSON.parse with bigints support", "main": "index.js", "files": ["index.js", "lib/parse.js", "lib/stringify.js"], "scripts": {"test": "./node_modules/mocha/bin/mocha -R spec --check-leaks test/*-test.js"}, "repository": {"type": "git", "url": "**************:sidorares/json-bigint.git"}, "keywords": ["JSON", "bigint", "bignumber", "parse", "json"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"bignumber.js": "^9.0.0"}, "devDependencies": {"chai": "4.2.0", "mocha": "8.0.1"}}